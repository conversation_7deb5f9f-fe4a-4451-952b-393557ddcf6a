package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class CosModelPredictServiceImplTest {

    @InjectMocks
    private CosModelPredictServiceImpl cosModelPredictService;

    @Test
    void testLinearFit() {
        // 准备测试数据
        List<QueryPredictResultResp.Line> lines = new ArrayList<>();
        
        // 创建历史数据线
        QueryPredictResultResp.Line historyLine = new QueryPredictResultResp.Line();
        historyLine.setScope("内部");
        historyLine.setType("HISTORY");
        historyLine.setPoints(new ArrayList<>());
        
        // 添加一些测试数据点（线性增长）
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        for (int i = 0; i < 10; i++) {
            LocalDate date = startDate.plusDays(i * 30); // 每30天一个点
            BigDecimal value = BigDecimal.valueOf(100 + i * 10); // 线性增长
            historyLine.getPoints().add(ListUtils.of(date, value));
        }
        
        lines.add(historyLine);
        
        // 创建配置
        CosLongtermPredictCategoryConfigDO categoryConfig = new CosLongtermPredictCategoryConfigDO();
        categoryConfig.setLinearStartDate(LocalDate.of(2023, 1, 1));
        categoryConfig.setPredictEnd("2023-12");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "linearFit", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);
            
            method.invoke(cosModelPredictService, lines, categoryConfig);
            
            // 验证结果
            assertEquals(2, lines.size()); // 原来1个历史线 + 1个预测线
            
            QueryPredictResultResp.Line predictLine = lines.stream()
                    .filter(line -> "PREDICT".equals(line.getType()))
                    .findFirst()
                    .orElse(null);
            
            assertNotNull(predictLine);
            assertEquals("内部", predictLine.getScope());
            assertEquals("PREDICT", predictLine.getType());
            assertEquals("LINEAR_FIT", predictLine.getAlgorithm());
            assertNotNull(predictLine.getAlgorithmParams());
            
            // 验证算法参数
            assertTrue(predictLine.getAlgorithmParams().containsKey("a"));
            assertTrue(predictLine.getAlgorithmParams().containsKey("b"));
            assertTrue(predictLine.getAlgorithmParams().containsKey("r2"));
            
            // 验证预测点数量（从2023-01-01到2023-12-31）
            assertNotNull(predictLine.getPoints());
            assertTrue(predictLine.getPoints().size() > 0);
            
            // 验证r²值应该接近1（因为是完美的线性数据）
            BigDecimal r2 = (BigDecimal) predictLine.getAlgorithmParams().get("r2");
            assertTrue(r2.doubleValue() > 0.9, "R²值应该很高，因为测试数据是完美线性的");
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testArimaPredict() {
        // 准备测试数据
        List<QueryPredictResultResp.Line> lines = new ArrayList<>();
        
        // 创建历史数据线
        QueryPredictResultResp.Line historyLine = new QueryPredictResultResp.Line();
        historyLine.setScope("内部");
        historyLine.setType("HISTORY");
        historyLine.setPoints(new ArrayList<>());
        
        // 添加一些测试数据点
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        for (int i = 0; i < 10; i++) {
            LocalDate date = startDate.plusDays(i * 7); // 每7天一个点
            BigDecimal value = BigDecimal.valueOf(100 + i * 5); // 递增数据
            historyLine.getPoints().add(ListUtils.of(date, value));
        }
        
        lines.add(historyLine);
        
        // 创建配置
        CosLongtermPredictCategoryConfigDO categoryConfig = new CosLongtermPredictCategoryConfigDO();
        categoryConfig.setPredictEnd("2023-04");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "arimaPredict", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);
            
            method.invoke(cosModelPredictService, lines, categoryConfig);
            
            // 验证结果 - 由于ARIMA调用外部服务，这里主要验证方法不会抛出异常
            // 实际的ARIMA预测可能会因为网络问题失败，所以我们主要验证代码逻辑
            assertTrue(lines.size() >= 1); // 至少有原来的历史线
            
            // 检查是否有PREDICT类型的线被添加（如果ARIMA调用成功的话）
            long predictLineCount = lines.stream()
                    .filter(line -> "PREDICT".equals(line.getType()) && "ARIMA".equals(line.getAlgorithm()))
                    .count();
            
            // 由于ARIMA依赖外部服务，预测线可能存在也可能不存在，这里不做强制断言
            assertTrue(predictLineCount >= 0);
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testArimaPredictWithInsufficientData() {
        // 准备测试数据 - 只有2个数据点（不足ARIMA要求的最少3个点）
        List<QueryPredictResultResp.Line> lines = new ArrayList<>();
        
        QueryPredictResultResp.Line historyLine = new QueryPredictResultResp.Line();
        historyLine.setScope("内部");
        historyLine.setType("HISTORY");
        historyLine.setPoints(new ArrayList<>());
        historyLine.getPoints().add(ListUtils.of(LocalDate.of(2023, 1, 1), BigDecimal.valueOf(100)));
        historyLine.getPoints().add(ListUtils.of(LocalDate.of(2023, 1, 8), BigDecimal.valueOf(105)));
        
        lines.add(historyLine);
        
        // 创建配置
        CosLongtermPredictCategoryConfigDO categoryConfig = new CosLongtermPredictCategoryConfigDO();
        categoryConfig.setPredictEnd("2023-02");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "arimaPredict", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);
            
            method.invoke(cosModelPredictService, lines, categoryConfig);
            
            // 验证结果 - 应该只有原来的历史线，没有预测线
            assertEquals(1, lines.size());
            assertEquals("HISTORY", lines.get(0).getType());
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testArimaPredictWithNullInput() {
        // 测试空输入
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "arimaPredict", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);
            
            // 测试null lines
            method.invoke(cosModelPredictService, null, new CosLongtermPredictCategoryConfigDO());
            
            // 测试null categoryConfig
            method.invoke(cosModelPredictService, new ArrayList<>(), null);
            
            // 如果没有抛出异常，测试通过
            assertTrue(true);
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }
}

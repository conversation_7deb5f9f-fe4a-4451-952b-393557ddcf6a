package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.utils.DateRangeUtils;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Component
public class CosCreatePredictTaskServiceImpl implements CosCreatePredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        List<CosLongtermPredictCategoryConfigDO> categoryConfigs = cdLabDbHelper.getAll(CosLongtermPredictCategoryConfigDO.class);

        QueryCategoryForCreateResp resp = new QueryCategoryForCreateResp();
        resp.setCategoryList(ListUtils.transform(categoryConfigs, o -> {
            QueryCategoryForCreateResp.Item item = new QueryCategoryForCreateResp.Item();
            item.setCategoryId(o.getId());
            item.setCategoryName(o.getCategory());
            LocalDate startDate = DateRangeUtils.getPredictStartDate(o);
            LocalDate endDate = DateRangeUtils.getPredictEndDate(o);
            item.setPredictStart(DateUtils.format(startDate, "yyyy-MM"));
            item.setPredictEnd(DateUtils.format(endDate, "yyyy-MM"));
            item.setInputArgDateRanges(DateRangeUtils.getDateRange(startDate, endDate, o.getIntervalMonth()));
            item.setStrategyTypes(ListUtils.transform(StrategyTypeEnum.values(), QueryCategoryForCreateResp.StrategyType::from));
            return item;
        }));
        return resp;
    }

    @Override
    public CosLongtermPredictCategoryConfigDO getCategoryById(Long id) {
        if (id == null) {
            return null;
        }
        return cdLabDbHelper.getByKey(CosLongtermPredictCategoryConfigDO.class, id);
    }

}

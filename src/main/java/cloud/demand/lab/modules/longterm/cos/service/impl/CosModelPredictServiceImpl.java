package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.service.CosModelPredictService;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.PlanCosScaleDataDTO;
import cloud.demand.lab.modules.longterm.cos.algorithm.ARIMA;
import cloud.demand.lab.modules.longterm.cos.algorithm.DateNumDTO;
import cloud.demand.lab.modules.longterm.cos.algorithm.PredictResult;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.regression.SimpleRegression;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CosModelPredictServiceImpl implements CosModelPredictService {

    @Resource
    private DBHelper planCosDBHelper;
    @Resource
    private CosBigCustomerHistoryChangeService cosBigCustomerHistoryChangeService;
    @Resource
    private CosCreatePredictTaskService cosCreatePredictTaskService;

    @Override
    public QueryPredictResultResp queryPredictResult(QueryPredictResultReq req) {
        CosLongtermPredictCategoryConfigDO categoryConfig = cosCreatePredictTaskService.getCategoryById(req.getCategoryId());
        if (categoryConfig == null) {
            throw new WrongWebParameterException("方案不存在，可能已经被删除了，请刷新页面重试");
        }

        QueryPredictResultResp resp = new QueryPredictResultResp();
        resp.setLines(new ArrayList<>());

        // 1. 查询大客户历史执行数据，用于在全量数据中进行剔除
        List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange =
                cosBigCustomerHistoryChangeService.queryBigCustomerHistoryChange(
                new QueryBigCustomerHistoryChangeReq(req.getCategoryId(), req.getTaskId())).getDataList();

        // 2. 查询历史数据，底数上分内外部，在内存中合并成全部
        if (req.getTaskId() == 0L) {
            queryHistoryRealtime(resp, bigCustomerHistoryChange);
        } else {
            // TODO 直接查有保存下来的底表数据
        }

        // 3. 预测数据
        if (req.getTaskId() == 0L) {
            // 3.1 一次线性拟合
            linearFit(resp.getLines(), categoryConfig);
            // 3.2 ARIMA等算法
            arimaPredict(resp.getLines(), categoryConfig);
        } else {
            // TODO 直接查有保存下来的底表数据
        }

        return resp;
    }


    private void arimaPredict(List<QueryPredictResultResp.Line> lines, CosLongtermPredictCategoryConfigDO categoryConfig) {
        if (lines == null || lines.isEmpty() || categoryConfig == null) {
            return;
        }

        // 筛选出所有type=HISTORY的线
        List<QueryPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));
        if (historyLines.isEmpty()) {
            return;
        }

        String predictEndStr = categoryConfig.getPredictEnd();
        if (StringTools.isBlank(predictEndStr)) {
            return;
        }

        // 解析预测结束时间，获取该月份的最后一天
        LocalDate predictEndDate;
        try {
            YearMonth predictEndYearMonth = YearMonth.parse(predictEndStr);
            predictEndDate = predictEndYearMonth.atEndOfMonth();
        } catch (Exception e) {
            log.error("解析预测结束时间失败: {}", predictEndStr, e);
            return;
        }

        // 对每个历史线进行ARIMA预测
        for (QueryPredictResultResp.Line historyLine : historyLines) {
            try {
                performArimaPredictForLine(historyLine, predictEndDate, lines, categoryConfig.getArimaArgs());
            } catch (Exception e) {
                log.error("对线{}进行ARIMA预测时发生错误", historyLine.getScope(), e);
            }
        }
    }

    /**
     * 对单个历史线进行ARIMA预测
     */
    private void performArimaPredictForLine(QueryPredictResultResp.Line historyLine, LocalDate predictEndDate,
                                          List<QueryPredictResultResp.Line> lines, String arimaArgs) {
        if (historyLine.getPoints() == null || historyLine.getPoints().isEmpty()) {
            return;
        }

        // 按日期排序历史数据点
        List<List<Object>> sortedPoints = new ArrayList<>(historyLine.getPoints());
        ListUtils.sortAscNullLast(sortedPoints, point -> (LocalDate) point.get(0));

        // 找到历史数据的最后日期
        LocalDate lastHistoryDate = (LocalDate) sortedPoints.get(sortedPoints.size() - 1).get(0);

        // 预测开始日期是历史数据结束日期的下一天
        LocalDate predictStartDate = lastHistoryDate.plusDays(1);

        if (predictStartDate.isAfter(predictEndDate)) {
            log.warn("ARIMA预测开始日期{}晚于结束日期{}，跳过预测", predictStartDate, predictEndDate);
            return;
        }

        // 准备ARIMA输入数据
        List<DateNumDTO> arimaInputData = new ArrayList<>();
        for (List<Object> point : sortedPoints) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);
            String dateStr = DateUtils.format(date, "yyyy-MM-dd");
            arimaInputData.add(new DateNumDTO(dateStr, value));
        }

        if (arimaInputData.size() < 3) {
            log.warn("ARIMA预测数据点不足，scope: {}, 数据点数量: {}", historyLine.getScope(), arimaInputData.size());
            return;
        }

        // 计算需要预测的天数
        long predictDays = ChronoUnit.DAYS.between(predictStartDate, predictEndDate) + 1;
        if (predictDays <= 0) {
            return;
        }

        try {
            // 使用ARIMA进行预测，使用自动选参（参数为-1）
            List<Integer> arimaParams = ListUtils.of(1,2,1);
            if (StringTools.isNotBlank(arimaArgs)) {
                arimaParams = ListUtils.transform(ListUtils.of(arimaArgs.split(",")), Integer::parseInt);
            }

            PredictResult predictResult = ARIMA.predict(arimaInputData, (int) predictDays, arimaParams);

            if (predictResult.getData() == null || predictResult.getData().isEmpty()) {
                log.warn("ARIMA预测返回空结果，scope: {}", historyLine.getScope());
                return;
            }

            // 创建预测线
            QueryPredictResultResp.Line predictLine = new QueryPredictResultResp.Line();
            predictLine.setScope(historyLine.getScope());
            predictLine.setType("PREDICT");
            predictLine.setAlgorithm("ARIMA");
            predictLine.setIncreaseInfos(new ArrayList<>());
            predictLine.setPoints(new ArrayList<>());

            // 设置算法参数
            Map<String, Object> algorithmParams = new HashMap<>();
            if (StringTools.isNotBlank(predictResult.getFallbackAlgorithm())) {
                algorithmParams.put("fallbackAlgorithm", predictResult.getFallbackAlgorithm());
            }
            predictLine.setAlgorithmParams(algorithmParams);

            // 转换预测结果为预测点
            LocalDate currentPredictDate = predictStartDate;
            for (DateNumDTO predictData : predictResult.getData()) {
                if (currentPredictDate.isAfter(predictEndDate)) {
                    break;
                }
                predictLine.getPoints().add(ListUtils.of(currentPredictDate, predictData.getValue()));
                currentPredictDate = currentPredictDate.plusDays(1);
            }

            // 将预测线添加到结果中
            lines.add(predictLine);

        } catch (Exception e) {
            log.error("ARIMA预测失败，scope: {}", historyLine.getScope(), e);
        }
    }

    private void linearFit(List<QueryPredictResultResp.Line> lines, CosLongtermPredictCategoryConfigDO categoryConfig) {
        if (lines == null || lines.isEmpty() || categoryConfig == null) {
            return;
        }

        // 筛选出所有type=HISTORY的线
        List<QueryPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));
        if (historyLines.isEmpty()) {
            return;
        }

        LocalDate linearStartDate = categoryConfig.getLinearStartDate();
        String predictEndStr = categoryConfig.getPredictEnd();
        if (linearStartDate == null || StringTools.isBlank(predictEndStr)) {
            return;
        }

        // 解析预测结束时间，获取该月份的最后一天
        LocalDate predictEndDate;
        try {
            YearMonth predictEndYearMonth = YearMonth.parse(predictEndStr);
            predictEndDate = predictEndYearMonth.atEndOfMonth();
        } catch (Exception e) {
            log.error("解析预测结束时间失败: {}", predictEndStr, e);
            return;
        }

        // 对每个历史线进行线性拟合预测
        for (QueryPredictResultResp.Line historyLine : historyLines) {
            try {
                performLinearFitForLine(historyLine, linearStartDate, predictEndDate, lines);
            } catch (Exception e) {
                log.error("对线{}进行线性拟合时发生错误", historyLine.getScope(), e);
            }
        }
    }

    /**
     * 对单个历史线进行线性拟合预测
     */
    private void performLinearFitForLine(QueryPredictResultResp.Line historyLine, LocalDate linearStartDate,
                                       LocalDate predictEndDate, List<QueryPredictResultResp.Line> lines) {
        if (historyLine.getPoints() == null || historyLine.getPoints().isEmpty()) {
            return;
        }

        // 筛选出从linearStartDate开始的数据点，并按日期排序
        List<List<Object>> filteredPoints = new ArrayList<>();
        LocalDate lastDataDate = null;

        for (List<Object> point : historyLine.getPoints()) {
            if (point.size() >= 2) {
                LocalDate date = (LocalDate) point.get(0);
                if (!date.isBefore(linearStartDate)) {
                    filteredPoints.add(point);
                    if (lastDataDate == null || date.isAfter(lastDataDate)) {
                        lastDataDate = date;
                    }
                }
            }
        }

        // 按日期排序
        ListUtils.sortAscNullLast(filteredPoints, point -> (LocalDate) point.get(0));

        if (filteredPoints.size() < 2) {
            log.warn("线性拟合数据点不足，scope: {}, 数据点数量: {}", historyLine.getScope(), filteredPoints.size());
            return;
        }

        // 准备线性回归数据
        SimpleRegression regression = new SimpleRegression();

        for (List<Object> point : filteredPoints) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            // 将日期转换为从linearStartDate开始的天数索引
            long dayIndex = ChronoUnit.DAYS.between(linearStartDate, date);
            regression.addData(dayIndex, value.doubleValue());
        }

        // 获取线性拟合参数
        double slope = regression.getSlope();        // a (斜率)
        double intercept = regression.getIntercept(); // b (截距)
        double rSquared = regression.getRSquare();    // r²

        // 创建预测线
        QueryPredictResultResp.Line predictLine = new QueryPredictResultResp.Line();
        predictLine.setScope(historyLine.getScope());
        predictLine.setType("PREDICT");
        predictLine.setAlgorithm("LINEAR_FIT");
        predictLine.setIncreaseInfos(new ArrayList<>());
        predictLine.setPoints(new ArrayList<>());

        // 设置算法参数
        Map<String, Object> algorithmParams = new HashMap<>();
        algorithmParams.put("a", BigDecimal.valueOf(slope));
        algorithmParams.put("b", BigDecimal.valueOf(intercept));
        algorithmParams.put("r2", BigDecimal.valueOf(rSquared));
        predictLine.setAlgorithmParams(algorithmParams);

        // 生成预测数据点：从linearStartDate到predictEndDate
        generatePredictPoints(predictLine, linearStartDate, predictEndDate, slope, intercept);

        // 将预测线添加到结果中
        lines.add(predictLine);
    }

    /**
     * 生成预测数据点
     */
    private void generatePredictPoints(QueryPredictResultResp.Line predictLine, LocalDate linearStartDate,
                                     LocalDate predictEndDate, double slope, double intercept) {
        LocalDate currentDate = linearStartDate;

        while (!currentDate.isAfter(predictEndDate)) {
            // 计算日期索引（从linearStartDate开始的天数）
            long dayIndex = ChronoUnit.DAYS.between(linearStartDate, currentDate);

            // 使用线性方程 y = ax + b 计算预测值
            double predictValue = slope * dayIndex + intercept;

            // 添加预测点
            predictLine.getPoints().add(ListUtils.of(currentDate, BigDecimal.valueOf(predictValue)));

            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }
    }

    @SneakyThrows
    private void queryHistoryRealtime(QueryPredictResultResp resp, List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange) {
        String sql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cos/plan_txy_cos_scale.sql");
        List<PlanCosScaleDataDTO> historyData = planCosDBHelper.getRaw(PlanCosScaleDataDTO.class, sql);

        // 3. 按scope分组数据
        Map<String, List<PlanCosScaleDataDTO>> scopeGroupedData = ListUtils.toMapList(historyData,
                o -> o.getScope(), o -> o);

        // 4.1 构建内部数据线
        QueryPredictResultResp.Line innerLine = buildHistoryLine("内部", scopeGroupedData.get("内部"),
                ListUtils.filter(bigCustomerHistoryChange, o -> !o.getIsOutCustomer()));
        resp.getLines().add(innerLine);
        // 4.2 构建外部数据线
        QueryPredictResultResp.Line outerLine = buildHistoryLine("外部", scopeGroupedData.get("外部"),
                ListUtils.filter(bigCustomerHistoryChange, o -> o.getIsOutCustomer()));
        resp.getLines().add(outerLine);

        // 4.3 构建全部数据线（内部+外部）
        QueryPredictResultResp.Line totalLine = buildTotalHistoryLine(innerLine, outerLine);
        resp.getLines().add(totalLine);
    }

    /**
     * 构建历史数据线
     * @param bigCustomerHistoryChange 大客户历史变动数据，在这个处理中，会将大客户的历史变动从总量中剔除
     */
    private QueryPredictResultResp.Line buildHistoryLine(String scope, List<PlanCosScaleDataDTO> data,
                                                         List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange) {
        QueryPredictResultResp.Line line = new QueryPredictResultResp.Line();
        line.setScope(scope);
        line.setType("HISTORY");
        line.setIncreaseInfos(new ArrayList<>());
        line.setPoints(new ArrayList<>());
        if (data != null) {
            for (PlanCosScaleDataDTO dto : data) {
                line.getPoints().add(ListUtils.of(dto.getDate(), dto.getValue()));
            }
        }

        // 剔除大客户的历史变化量，剔除时间从早到晚进行
        ListUtils.sortAscNullLast(bigCustomerHistoryChange, o -> o.getStartDate());
        // 对每个大客户历史变动进行调整
        if (bigCustomerHistoryChange != null && !bigCustomerHistoryChange.isEmpty()) {
            for (BigCustomerHistoryChangeDTO change : bigCustomerHistoryChange) {
                LocalDate startDate = change.getStartDate();
                LocalDate endDate = change.getEndDate();
                BigDecimal delta = change.getNetChange();

                if (startDate != null && delta != null) {
                    delta = delta.negate(); // 底表录的是大客户的历史执行量，在剔除时，等于是量反过来

                    // 先进行趋势调整（在指定时间段内）
                    adjustTrend(line.getPoints(), startDate, endDate, delta);
                    // 再进行直接调整（在结束时间之后的所有数据点）
                    if (endDate != null) {
                        adjustDirect(line.getPoints(), endDate.plusDays(1), null, delta);
                    }
                }
            }
        }

        return line;
    }

    /**
     * 构建全部数据线（内部+外部合并）
     */
    private QueryPredictResultResp.Line buildTotalHistoryLine(QueryPredictResultResp.Line innerLine, QueryPredictResultResp.Line outerLine) {
        // 按日期分组，然后合并内外部数据
        Map<LocalDate, BigDecimal> dateValueMap = new HashMap<>();

        // 处理内部数据线的点
        if (innerLine != null && innerLine.getPoints() != null) {
            for (List<Object> point : innerLine.getPoints()) {
                LocalDate date = (LocalDate) point.get(0);
                BigDecimal value = (BigDecimal) point.get(1);
                dateValueMap.merge(date, value, BigDecimal::add);
            }
        }

        // 处理外部数据线的点
        if (outerLine != null && outerLine.getPoints() != null) {
            for (List<Object> point : outerLine.getPoints()) {
                LocalDate date = (LocalDate) point.get(0);
                BigDecimal value = (BigDecimal) point.get(1);
                dateValueMap.merge(date, value, BigDecimal::add);
            }
        }

        QueryPredictResultResp.Line line = new QueryPredictResultResp.Line();
        line.setScope("全部");
        line.setType("HISTORY");
        line.setIncreaseInfos(new ArrayList<>());
        line.setPoints(new ArrayList<>());
        for (Map.Entry<LocalDate, BigDecimal> entry : dateValueMap.entrySet()) {
            line.getPoints().add(ListUtils.of(entry.getKey(), entry.getValue()));
        }
        ListUtils.sortAscNullLast(line.getPoints(), o -> (LocalDate) o.get(0));

        return line;
    }

    /**
     * 按趋势调整delta值，如果delta为正数，则表示调高趋势；如果delta为负数，则表示调低趋势。
     *
     * @param points 数据点列表，格式为[LocalDate, BigDecimal]
     * @param startDate 开始日期
     * @param endDate 结束日期，如果为null则使用最后一个数据点的日期
     * @param delta 调整量
     */
    private void adjustTrend(List<List<Object>> points, LocalDate startDate, LocalDate endDate, BigDecimal delta) {
        if (ListUtils.isEmpty(points) || startDate == null || delta == null) {
            return;
        }

        // 如果endDate为null，使用最后一个数据点的日期
        if (endDate == null) {
            endDate = (LocalDate) points.get(points.size() - 1).get(0);
        }

        // 找到开始和结束日期对应的数据点
        BigDecimal startValue = null;
        BigDecimal endValue = null;

        for (List<Object> point : points) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            if (date.equals(startDate)) {
                startValue = value;
            }
            if (date.equals(endDate)) {
                endValue = value;
            }
        }

        // 如果找不到对应的数据点，则不进行调整
        if (startValue == null || endValue == null) {
            return;
        }

        // 对指定时间段内的数据点进行趋势调整
        for (List<Object> point : points) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                // 应用趋势调整公式: (y + delta) - (y - value) / (y - x) * (y - x + delta)
                // 其中 x = startValue, y = endValue, value = 当前值
                BigDecimal adjustedValue;
                if (endValue.equals(startValue)) {
                    // 避免除零，如果起始值相等，直接加上delta
                    adjustedValue = value.add(delta);
                } else {
                    // 重新理解Python公式: new_value = (y + delta) - (y - value) / (y - x) * (y - x + delta)
                    BigDecimal yPlusDelta = endValue.add(delta);
                    BigDecimal yMinusValue = endValue.subtract(value);
                    BigDecimal yMinusX = endValue.subtract(startValue);
                    BigDecimal yMinusXPlusDelta = yMinusX.add(delta);

                    if (yMinusX.compareTo(BigDecimal.ZERO) == 0) {
                        // 如果y == x，直接加delta
                        adjustedValue = value.add(delta);
                    } else {
                        BigDecimal ratio = yMinusValue.divide(yMinusX, 6, RoundingMode.HALF_UP);
                        adjustedValue = yPlusDelta.subtract(ratio.multiply(yMinusXPlusDelta));
                    }
                }

                point.set(1, adjustedValue);
            }
        }
    }

    /**
     * 直接调整指定时间段内的值，适用于直接剔除或添加固定量。
     *
     * @param points 数据点列表，格式为[LocalDate, BigDecimal]
     * @param startDate 开始日期，如果为null则从第一个数据点开始
     * @param endDate 结束日期，如果为null则到最后一个数据点结束
     * @param delta 调整量
     */
    private void adjustDirect(List<List<Object>> points, LocalDate startDate, LocalDate endDate, BigDecimal delta) {
        if (ListUtils.isEmpty(points) || delta == null) {
            return;
        }

        // 如果startDate为null，从第一个数据点开始
        if (startDate == null) {
            startDate = (LocalDate) points.get(0).get(0);
        }

        // 如果endDate为null，到最后一个数据点结束
        if (endDate == null) {
            endDate = (LocalDate) points.get(points.size() - 1).get(0);
        }

        // 对指定时间段内的数据点进行直接调整
        for (List<Object> point : points) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                point.set(1, value.add(delta));
            }
        }
    }

}

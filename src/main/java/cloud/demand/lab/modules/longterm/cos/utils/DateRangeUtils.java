package cloud.demand.lab.modules.longterm.cos.utils;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgDateRangeDTO;
import com.pugwoo.wooutils.lang.DateUtils;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于计算中长期输入参数每半年的日期名称和范围
 */
public class DateRangeUtils {

    /**
     * 计算中长期输入参数每半年的日期名称和范围
     * @param startDate 日期开始
     * @param endDate 日期结束
     * @param intervalMonth 间隔月份
     * @return 日期列表
     */
    public static List<InputArgDateRangeDTO> getDateRange(LocalDate startDate, LocalDate endDate,
                                                    int intervalMonth) {
        if (intervalMonth == 6) { // 每半年作为1个录入周期
            if (startDate.getMonthValue() >= 7) {
                startDate = LocalDate.of(startDate.getYear(), 6, 30);
            } else {
                startDate = LocalDate.of(startDate.getYear() - 1, 12, 31);
            }
            if (endDate.getMonthValue() >= 7) {
                endDate = LocalDate.of(endDate.getYear(), 12, 31);
            } else {
                endDate = LocalDate.of(endDate.getYear(), 6, 30);
            }

            List<InputArgDateRangeDTO> ranges = new ArrayList<>();

            while (startDate.isBefore(endDate)) {
                InputArgDateRangeDTO range = new InputArgDateRangeDTO();
                if (startDate.getMonthValue() == 6) {
                    range.setDateName(startDate.getYear() + "下半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(rangeStartDate);
                    range.setEndDate(rangeStartDate.plusMonths(6).minusDays(1));
                } else {
                    range.setDateName((startDate.getYear() + 1) + "上半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(rangeStartDate);
                    range.setEndDate(rangeStartDate.plusMonths(6).minusDays(1));
                }
                ranges.add(range);

                startDate = startDate.plusDays(1).plusMonths(6).minusDays(1);
            }

            return ranges;
        } else if (intervalMonth == 12) { // 每年作为1个录入周期
            startDate = LocalDate.of(startDate.getYear(), 1, 1);
            endDate = LocalDate.of(endDate.getYear(), 12, 31);
            List<InputArgDateRangeDTO> ranges = new ArrayList<>();
            while (startDate.isBefore(endDate)) {
                InputArgDateRangeDTO range = new InputArgDateRangeDTO();
                range.setDateName(startDate.getYear() + "年");
                range.setStartDate(startDate);
                range.setEndDate(LocalDate.of(startDate.getYear(), 12, 31));
                ranges.add(range);

                startDate = startDate.plusYears(1);
            }
            return ranges;
        } else {
            return new ArrayList<>();
        }
    }

    /**都以月末为表示*/
    public static LocalDate getPredictStartDate(CosLongtermPredictCategoryConfigDO categoryConfigDO) {
        if ("CUR_MONTH".equals(categoryConfigDO.getPredictStart())) {
            return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        } else {
            LocalDate startDate = DateUtils.parseLocalDate(categoryConfigDO.getPredictStart());
            if (startDate == null) {
                return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()); // 默认当月
            }
            return startDate.with(TemporalAdjusters.lastDayOfMonth());
        }
    }

    /**都以月末为表示*/
    public static LocalDate getPredictEndDate(CosLongtermPredictCategoryConfigDO categoryConfigDO) {
        return DateUtils.parseLocalDate(categoryConfigDO.getPredictEnd()).with(TemporalAdjusters.lastDayOfMonth());
    }

}
